import React from 'react';
import { Routes, Route } from 'react-router-dom';

// Import components (these will need to be created or imported from frontend)
const Header: React.FC = () => (
  <header style={{ backgroundColor: '#282c34', padding: '1rem 2rem' }}>
    <nav style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', maxWidth: '1200px', margin: '0 auto' }}>
      <a href="/" style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#61dafb', textDecoration: 'none' }}>
        Caivoo
      </a>
      <ul style={{ display: 'flex', listStyle: 'none', margin: 0, padding: 0, gap: '2rem' }}>
        <li>
          <a href="/" style={{ color: '#ffffff', textDecoration: 'none', fontWeight: '500' }}>
            Home
          </a>
        </li>
        <li>
          <a href="/about" style={{ color: '#ffffff', textDecoration: 'none', fontWeight: '500' }}>
            About
          </a>
        </li>
      </ul>
    </nav>
  </header>
);

const HomePage: React.FC = () => (
  <div style={{ minHeight: '100vh' }}>
    <Header />
    <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem', textAlign: 'center' }}>
      <h1 style={{ color: '#282c34', fontSize: '2.5rem', marginBottom: '1rem' }}>Welcome to Caivoo</h1>
      <p style={{ color: '#666', fontSize: '1.2rem', lineHeight: '1.6' }}>
        This is a React TypeScript application with SSR support.
      </p>
    </main>
  </div>
);

const AboutPage: React.FC = () => (
  <div style={{ minHeight: '100vh' }}>
    <Header />
    <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem', textAlign: 'center' }}>
      <h1 style={{ color: '#282c34', fontSize: '2.5rem', marginBottom: '1rem' }}>About Caivoo</h1>
      <p style={{ color: '#666', fontSize: '1.2rem', lineHeight: '1.6' }}>
        This application demonstrates React with TypeScript, Webpack, Sass, and SSR.
      </p>
    </main>
  </div>
);

const App: React.FC = () => {
  return (
    <div style={{ minHeight: '100vh', fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif', backgroundColor: '#f5f5f5' }}>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/about" element={<AboutPage />} />
      </Routes>
    </div>
  );
};

export default App;
