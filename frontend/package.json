{"name": "caivoo-frontend", "version": "1.0.0", "description": "React TypeScript frontend with Webpack and Sass", "private": true, "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "build:dev": "webpack --mode development", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0"}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@webpack-cli/generators": "^3.0.7", "css-loader": "^6.8.1", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "sass": "^1.66.1", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}