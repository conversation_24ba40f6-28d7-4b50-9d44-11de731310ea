import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON>rowser<PERSON>outer as Router } from 'react-router-dom';
import App from '../../shared/components/App';

const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

const root = createRoot(container);
root.render(
  <React.StrictMode>
    <Router>
      <App />
    </Router>
  </React.StrictMode>
);
