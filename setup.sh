#!/bin/bash

echo "🚀 Setting up Caivoo project..."

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for Node.js
if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js >= 18.0.0"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please install Node.js >= 18.0.0"
    exit 1
fi

echo "✅ Node.js version $NODE_VERSION detected"

# Fix npm cache permissions if needed
echo "🔧 Fixing npm cache permissions..."
if [ -d "$HOME/.npm" ]; then
    sudo chown -R $(whoami) "$HOME/.npm" 2>/dev/null || echo "⚠️  Could not fix npm permissions. You may need to run: sudo chown -R \$(whoami) ~/.npm"
fi

# Clean npm cache
echo "🧹 Cleaning npm cache..."
npm cache clean --force 2>/dev/null || echo "⚠️  Could not clean npm cache"

# Install dependencies
echo "📦 Installing dependencies..."

# Try to install root dependencies
echo "Installing root dependencies..."
npm install --no-optional --legacy-peer-deps || {
    echo "⚠️  Root npm install failed, trying with yarn..."
    if command_exists yarn; then
        yarn install
    else
        echo "❌ npm install failed and yarn is not available"
    fi
}

# Install frontend dependencies
echo "Installing frontend dependencies..."
cd frontend
npm install --no-optional --legacy-peer-deps || {
    echo "⚠️  Frontend npm install failed, trying with yarn..."
    if command_exists yarn; then
        yarn install
    else
        echo "❌ Frontend npm install failed and yarn is not available"
    fi
}

# Install backend dependencies
echo "Installing backend dependencies..."
cd ../backend
npm install --no-optional --legacy-peer-deps || {
    echo "⚠️  Backend npm install failed, trying with yarn..."
    if command_exists yarn; then
        yarn install
    else
        echo "❌ Backend npm install failed and yarn is not available"
    fi
}

cd ..

echo "✅ Setup complete!"
echo ""
echo "🎉 Next steps:"
echo "1. Run 'npm run dev' to start both frontend and backend"
echo "2. Visit http://localhost:5000 for SSR version"
echo "3. Visit http://localhost:3000 for frontend-only version"
echo ""
echo "📚 See README.md for more information"
