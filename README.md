# Caivoo - React TypeScript with SSR

A modern full-stack application with React TypeScript frontend and Express.js backend with Server-Side Rendering (SSR) support.

## Project Structure

```
Caivoo/
├── frontend/                 # React TypeScript frontend
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── pages/          # Page components
│   │   ├── routers/        # Routing configuration
│   │   ├── App.tsx         # Main App component
│   │   └── index.tsx       # Entry point
│   ├── public/             # Static assets
│   ├── webpack.config.js   # Webpack configuration
│   ├── tsconfig.json       # TypeScript config
│   └── package.json        # Frontend dependencies
├── backend/                 # Express.js backend
│   ├── src/
│   │   └── server.ts       # Express server with SSR
│   ├── tsconfig.json       # TypeScript config
│   └── package.json        # Backend dependencies
├── shared/                  # Shared components
│   └── components/
│       └── App.tsx         # Shared App component for SSR
└── package.json            # Root package.json
```

## Features

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Webpack 5 with hot reload
- **Styling**: Sass with CSS Modules
- **Backend**: Express.js with TypeScript
- **SSR**: Server-Side Rendering support
- **Routing**: React Router v6
- **Development**: Hot reload for both frontend and backend

## Prerequisites

- Node.js >= 18.0.0
- npm >= 8.0.0

## Setup Instructions

### 1. Fix npm cache permissions (if needed)

If you encounter npm cache permission errors, run:

```bash
sudo chown -R $(whoami) ~/.npm
```

Or alternatively, use yarn:

```bash
npm install -g yarn
```

### 2. Install Dependencies

#### Option A: Using npm workspaces (recommended)
```bash
npm install
```

#### Option B: Install individually
```bash
# Install root dependencies
npm install

# Install frontend dependencies
cd frontend && npm install

# Install backend dependencies
cd ../backend && npm install
```

#### Option C: Using yarn
```bash
# Install yarn globally if not installed
npm install -g yarn

# Install all dependencies
yarn install

# Or install individually
cd frontend && yarn install
cd ../backend && yarn install
```

### 3. Development

#### Start both frontend and backend:
```bash
npm run dev
```

#### Start individually:
```bash
# Frontend only (port 3000)
npm run dev:frontend

# Backend only (port 5000)
npm run dev:backend
```

### 4. Building for Production

```bash
# Build both
npm run build

# Build individually
npm run build:frontend
npm run build:backend
```

### 5. Production

```bash
npm start
```

## Available Scripts

### Root Level
- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both frontend and backend for production
- `npm start` - Start the production server

### Frontend
- `npm run dev` - Start Webpack dev server (port 3000)
- `npm run build` - Build for production
- `npm run type-check` - Run TypeScript type checking

### Backend
- `npm run dev` - Start development server with hot reload (port 5000)
- `npm run build` - Compile TypeScript to JavaScript
- `npm start` - Start production server
- `npm run type-check` - Run TypeScript type checking

## Technology Stack

### Frontend
- **React 18** - UI library
- **TypeScript** - Type safety
- **Webpack 5** - Module bundler
- **Sass** - CSS preprocessor
- **CSS Modules** - Scoped styling
- **React Router v6** - Client-side routing

### Backend
- **Express.js** - Web framework
- **TypeScript** - Type safety
- **React SSR** - Server-side rendering
- **ts-node-dev** - Development server

## Development Workflow

1. **Frontend Development**: 
   - Webpack dev server runs on port 3000
   - Hot reload enabled
   - CSS modules with Sass preprocessing

2. **Backend Development**:
   - Express server runs on port 5000
   - Auto-restart on file changes
   - Serves SSR-rendered React app

3. **SSR Flow**:
   - Express server renders React components server-side
   - Hydrates on the client for interactivity
   - Shared App component between client and server

## Troubleshooting

### npm Cache Issues
If you encounter permission errors with npm cache:
```bash
sudo chown -R $(whoami) ~/.npm
npm cache clean --force
```

### TypeScript Errors
Make sure all dependencies are installed:
```bash
npm install
cd frontend && npm install
cd ../backend && npm install
```

### Port Conflicts
- Frontend dev server: http://localhost:3000
- Backend server: http://localhost:5000
- Change ports in package.json scripts if needed

## Next Steps

1. Install dependencies using one of the methods above
2. Run `npm run dev` to start development
3. Visit http://localhost:5000 for SSR or http://localhost:3000 for client-only
4. Start building your application!
