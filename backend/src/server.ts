import express from 'express';
import path from 'path';
import { renderToString } from 'react-dom/server';
import React from 'react';
import { StaticRouter } from 'react-router-dom/server';
import App from '../../shared/components/App';

const app = express();
const PORT = process.env.PORT || 5000;

// Serve static files from the frontend build
app.use(express.static(path.join(__dirname, '../../frontend/dist')));

// API routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

app.get('/api/data', (req, res) => {
  res.json({ 
    message: 'Hello from the backend!',
    timestamp: new Date().toISOString()
  });
});

// SSR route handler
app.get('*', (req, res) => {
  try {
    // Create the React app with StaticRouter for SSR
    const appMarkup = renderToString(
      React.createElement(StaticRouter, { location: req.url }, 
        React.createElement(App)
      )
    );

    // Read the HTML template
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="utf-8" />
          <link rel="icon" href="/favicon.ico" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <meta name="theme-color" content="#000000" />
          <meta name="description" content="Caivoo - React TypeScript Application" />
          <title>Caivoo</title>
      </head>
      <body>
          <noscript>You need to enable JavaScript to run this app.</noscript>
          <div id="root">${appMarkup}</div>
          <script src="/main.js"></script>
      </body>
      </html>
    `;

    res.send(html);
  } catch (error) {
    console.error('SSR Error:', error);
    res.status(500).send('Internal Server Error');
  }
});

app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
