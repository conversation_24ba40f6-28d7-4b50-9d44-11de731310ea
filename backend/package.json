{"name": "caivoo-backend", "version": "1.0.0", "description": "Express TypeScript backend with SSR support", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.9", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}